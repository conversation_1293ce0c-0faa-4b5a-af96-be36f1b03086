package com.sky.mapper;

import com.sky.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.HashMap;
import java.util.Map;

/**
 * @return
 */

@Mapper
public interface UserMapper {

    /**
     * 根据openid查询用户
     * @param openid
     * @return
     */
    @Select("SELECT * from user where openid = #{openid}")
    User getByOpenid(String openid);


    /**
     * 插入数据
     * @param user
     * @return
     */
    void insert(User user);

    @Select("select * from user where id = #{id}")
    User getById(Long id);

    Integer sumBymap(Map map);
}
