package com.sky.aspect;

import com.sky.annotation.AutoFill;
import com.sky.constant.AutoFillConstant;
import com.sky.context.BaseContext;
import com.sky.enumeration.OperationType;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;

/**
 * @return
 */

@Aspect
@Component
@Slf4j
public class AutoFillAspect {
    /**
     * 切入点
     */
    @Pointcut("@annotation(com.sky.annotation.AutoFill)")
    public void AutoFillPointcut(){}

    /**
     * 设置前置通知
     */
    @Before("AutoFillPointcut()")
    public void autoFill(JoinPoint joinPoint) {
        log.info("开始填充公共字段");

        //先获取当前被拦截方法上的数据库操作类型
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        AutoFill autoFill = methodSignature.getMethod().getAnnotation(AutoFill.class);
        OperationType operationType = autoFill.value();
        log.info("当前操作类型：{}", operationType);

        //然后获取被拦截方法的参数--实体对象
        Object[] args = joinPoint.getArgs();
        if(args == null || args.length ==0) {
            log.warn("方法参数为空，无法进行公共字段填充");
            return;
        }
        Object entity = args[0];
        log.info("当前实体类型：{}", entity.getClass().getName());

        //准备赋值的数据
        LocalDateTime now = LocalDateTime.now();
        Long id = BaseContext.getCurrentId();
        log.info("准备填充的数据 - 时间：{}，用户ID：{}", now, id);

        //根据不同的操作类型， 为对应的属性通过反射赋值
        if(operationType.equals(OperationType.INSERT)) {
            try {
                //通过反射获取set方法，传入的是方法名和该方法需要传入的参数类型
                Method setCreateTime = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_CREATE_TIME, LocalDateTime.class);
                Method setCreateUser = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_CREATE_USER, Long.class);
                Method setUpdateTime = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_TIME, LocalDateTime.class);
                Method setUpdateUser = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_USER, Long.class);

                //通过反射为属性赋值, 调用invoke方法，传入需要赋值的对象和值
                setCreateTime.invoke(entity, now);
                setCreateUser.invoke(entity,id);
                setUpdateTime.invoke(entity, now);
                setUpdateUser.invoke(entity, id);
                log.info("INSERT操作 - 公共字段填充完成");
            } catch (Exception e) {
                log.error("INSERT操作 - 公共字段填充失败", e);
                throw new RuntimeException(e);
            }
        } else if(operationType.equals(OperationType.UPDATE)) {
            try {
                //通过反射获取set方法，传入的是方法名和该方法需要传入的参数类型
                Method setUpdateTime = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_TIME, LocalDateTime.class);
                Method setUpdateUser = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_USER, Long.class);

                //通过反射为属性赋值, 调用invoke方法，传入需要赋值的对象和值
                setUpdateTime.invoke(entity, now);
                setUpdateUser.invoke(entity, id);
                log.info("UPDATE操作 - 公共字段填充完成");
            } catch (Exception e) {
                log.error("UPDATE操作 - 公共字段填充失败", e);
                throw new RuntimeException(e);
            }
        }
    }
}
